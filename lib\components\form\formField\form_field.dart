import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';

/// 表单字段标签位置枚举
enum FormFieldLabelPosition {
  left, // 左侧标签
  top, // 上方标签
}

/// 可复用的表单字段组件
///
/// 封装带标签和验证的表单字段通用模式，包括：
/// - 左侧/上方标签显示（支持必填标记 *）
/// - 表单控件区域
/// - 验证错误信息显示
class AppFormField extends StatelessWidget {
  /// 标签文本
  final String label;

  /// 是否必填，控制红色星号显示
  final bool required;

  /// 标签宽度（仅在 labelPosition 为 left 时有效）
  final double? labelWidth;

  /// 标签位置
  final FormFieldLabelPosition labelPosition;

  /// 验证函数，从外部传入
  final String? Function(dynamic)? validator;

  /// 表单控件构建器，提供 FormFieldState 供外部使用
  final Widget Function(FormFieldState<dynamic> field)? builder;

  /// 表单控件widget（当不使用 builder 时使用）
  final Widget? child;

  /// 表单控制器（可选，泛型支持）
  final dynamic controller;

  const AppFormField({
    super.key,
    required this.label,
    this.required = false,
    this.labelWidth,
    this.labelPosition = FormFieldLabelPosition.left,
    this.validator,
    this.builder,
    this.child,
    this.controller,
  }) : assert(
         builder != null || child != null,
         'Either builder or child must be provided',
       );

  @override
  Widget build(BuildContext context) {
    return FormField<dynamic>(
      validator: validator,
      builder: (FormFieldState<dynamic> field) {
        if (labelPosition == FormFieldLabelPosition.left) {
          return _buildLeftLabelLayout(field);
        } else {
          return _buildTopLabelLayout(field);
        }
      },
    );
  }

  /// 构建左侧标签布局
  Widget _buildLeftLabelLayout(FormFieldState<dynamic> field) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧标签区域
        SizedBox(width: labelWidth ?? 80, child: _buildLabel()),
        // 右侧表单控件区域
        Expanded(
          child: Column(
            children: [_buildFormControl(field), _buildValidatorMsg(field)],
          ),
        ),
      ],
    );
  }

  /// 构建上方标签布局
  Widget _buildTopLabelLayout(FormFieldState<dynamic> field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 上方标签
        _buildLabel(),
        const SizedBox(height: 8),
        // 表单控件
        _buildFormControl(field),
        // 验证错误信息
        _buildValidatorMsg(field),
      ],
    );
  }

  /// 构建表单控件
  Widget _buildFormControl(FormFieldState<dynamic> field) {
    if (builder != null) {
      return builder!(field);
    } else {
      return child!;
    }
  }

  /// 构建标签组件
  Widget _buildLabel() {
    return Padding(
      padding: EdgeInsets.only(top: required ? 2 : 6),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          if (required)
            const Text(
              ' *',
              style: TextStyle(fontSize: 20, color: AppColors.error),
            ),
        ],
      ),
    );
  }

  /// 构建验证错误信息显示组件
  ///
  /// 复用 ValidatorMsg 的逻辑：
  /// - 固定高度 18px
  /// - 左对齐
  /// - 200ms 淡入淡出动画
  /// - 错误文本样式：11px，红色，行高1.2
  /// - 单行显示，超出省略
  Widget _buildValidatorMsg(FormFieldState<dynamic> field) {
    return SizedBox(
      height: 18,
      child: Align(
        alignment: Alignment.topLeft,
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 200),
          opacity: field.hasError ? 1.0 : 0.0,
          child: Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              field.hasError ? (field.errorText ?? '') : '',
              style: const TextStyle(
                fontSize: 11,
                color: AppColors.error,
                height: 1.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }
}
