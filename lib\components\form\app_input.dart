import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jyt_components_package/components/basic/app_button.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/input_theme.dart';

/// 输入框尺寸枚举
enum InputSize {
  large, // 大型输入框
  medium, // 中型输入框
  small, // 小型输入框
  mini, // 迷你输入框
}

/// 标签位置枚举
enum LabelPosition {
  top, // 标签在顶部
  left, // 标签在左侧
}

/// 自定义输入框组件
class AppInput extends StatefulWidget {
  final String? label;

  /// 标签位置，默认在顶部
  final LabelPosition labelPosition;

  /// 标签宽度，仅在标签位置为左侧时生效，为null时自适应宽度
  final double? labelWidth;

  /// 是否为必填字段
  final bool required;

  /// 自定义错误文本，通常不需要手动设置，会使用validator的返回值
  final String? errorText;

  /// 是否显示校验的错误信息(显示输入框下面会有占位)
  final bool? showErrMsg;

  /// 输入框的控制器
  final TextEditingController? controller;

  /// 键盘类型，默认为普通文本
  final TextInputType keyboardType;

  /// 是否隐藏输入内容，用于密码输入
  final bool obscureText;

  /// 输入框提示文本，显示在输入框内部
  final String? hintText;

  /// 输入框前缀图标
  final Widget? prefixIcon;

  /// 输入框后缀图标
  final Widget? suffixIcon;

  /// 输入内容变化时的回调函数
  final Function(String)? onChanged;

  /// 验证函数，返回null表示验证通过，返回字符串表示错误信息
  final String? Function(String?)? validator;

  /// 焦点节点，用于控制输入框的焦点
  final FocusNode? focusNode;

  /// 自动验证模式，默认为失焦
  final AutovalidateMode autovalidateMode;

  /// 最大输入长度限制
  final int? maxLength;

  /// 最大行数，默认为1行。设置大于1的值可将输入框变为文本域
  final int maxLines;

  /// 是否显示字符计数器
  final bool showCounter;

  /// 是否禁用输入框，true表示禁用，false表示启用
  final bool disabled;

  /// 是否显示清除按钮
  final bool showClearButton;

  /// 输入框尺寸
  final InputSize size;

  /// 是否自动获取焦点
  final bool autofocus;

  /// 输入框宽度，为null时将占满父容器宽度
  final double? width;

  /// 输入框失焦时的回调函数
  final VoidCallback? onBlur;

  /// 输入格式化器
  final List<TextInputFormatter>? inputFormatters;

  const AppInput({
    super.key,
    this.label,
    this.labelPosition = LabelPosition.top,
    this.labelWidth,
    this.required = false,
    this.errorText,
    this.showErrMsg = true,
    this.controller,
    this.keyboardType = TextInputType.text,
    this.obscureText = false,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.onChanged,
    this.validator,
    this.focusNode,
    this.autovalidateMode = AutovalidateMode.onUnfocus,
    this.maxLength,
    this.maxLines = 1,
    this.showCounter = false,
    this.disabled = false,
    this.showClearButton = true,
    this.size = InputSize.medium,
    this.autofocus = false,
    this.width,
    this.onBlur,
    this.inputFormatters,
  });

  @override
  State<AppInput> createState() => _AppInputState();
}

class _AppInputState extends State<AppInput> {
  // 内部控制器，当外部未提供控制器时使用
  TextEditingController? _internalController;

  // 用于监听焦点变化的FocusNode
  FocusNode? _internalFocusNode;

  // 鼠标悬停状态
  bool _isHovering = false;

  // 存储上一次的文本值，用于比较是否发生变化
  String _lastText = '';

  // 用于引用FormField的Key
  final GlobalKey<FormFieldState<String>> _formFieldKey = GlobalKey<FormFieldState<String>>();

  // 获取当前使用的控制器
  TextEditingController get _controller => widget.controller ?? _internalController!;

  // 获取当前使用的焦点节点
  FocusNode get _focusNode => widget.focusNode ?? _internalFocusNode!;

  /// 获取输入框高度
  double get _inputHeight {
    // 当为多行文本时，返回null以允许自动扩展高度
    if (widget.maxLines > 1) {
      return widget.maxLines * _getSingleLineHeight();
    }

    // 单行文本使用固定高度
    switch (widget.size) {
      case InputSize.large:
        return 44.0;
      case InputSize.medium:
        return 36.0;
      case InputSize.small:
        return 28.0;
      case InputSize.mini:
        return 24.0;
    }
  }

  /// 获取错误信息区域的固定高度
  double get _errorAreaHeight {
    switch (widget.size) {
      case InputSize.large:
        return 20.0; // 错误文本 + 间距
      case InputSize.medium:
        return 18.0;
      case InputSize.small:
        return 16.0;
      case InputSize.mini:
        return 14.0;
    }
  }

  /// 获取错误文本的字体大小
  double get _errorFontSize {
    switch (widget.size) {
      case InputSize.large:
        return 12.0;
      case InputSize.medium:
        return 11.0;
      case InputSize.small:
        return 10.0;
      case InputSize.mini:
        return 9.0;
    }
  }

  /// 获取单行文本的高度
  double _getSingleLineHeight() {
    switch (widget.size) {
      case InputSize.large:
        return 22.0;
      case InputSize.medium:
        return 18.0;
      case InputSize.small:
        return 14.0;
      case InputSize.mini:
        return 12.0;
    }
  }

  /// 获取字体大小
  double get _fontSize {
    switch (widget.size) {
      case InputSize.large:
        return 16.0;
      case InputSize.medium:
        return 14.0;
      case InputSize.small:
        return 12.0;
      case InputSize.mini:
        return 12.0;
    }
  }

  /// 获取内容内边距
  EdgeInsets get _contentPadding {
    // 多行文本需要调整垂直内边距
    if (widget.maxLines > 1) {
      switch (widget.size) {
        case InputSize.large:
          return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0);
        case InputSize.medium:
          return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
        case InputSize.small:
          return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0);
        case InputSize.mini:
          return const EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0);
      }
    }

    // 单行文本内边距保持不变
    switch (widget.size) {
      case InputSize.large:
        return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0);
      case InputSize.medium:
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0.0);
      case InputSize.small:
        return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0.0);
      case InputSize.mini:
        return const EdgeInsets.symmetric(horizontal: 4.0, vertical: 0.0);
    }
  }

  @override
  void initState() {
    super.initState();
    // 如果没有提供外部控制器，创建一个内部控制器
    if (widget.controller == null) {
      _internalController = TextEditingController();
    }

    // 如果没有提供外部焦点节点，创建一个内部焦点节点
    if (widget.focusNode == null) {
      _internalFocusNode = FocusNode();
    }

    // 初始化_lastText为控制器的初始文本
    _lastText = _controller.text;

    // 添加焦点变化监听器
    _focusNode.addListener(_onFocusChanged);

    // 添加控制器监听器
    _controller.addListener(_onControllerChanged);
  }

  @override
  void dispose() {
    // 移除控制器监听器
    _controller.removeListener(_onControllerChanged);

    // 移除焦点变化监听器
    _focusNode.removeListener(_onFocusChanged);

    // 如果使用的是内部控制器，需要在组件销毁时释放
    _internalController?.dispose();

    // 如果使用的是内部焦点节点，需要在组件销毁时释放
    _internalFocusNode?.dispose();

    super.dispose();
  }

  // 焦点变化时的回调
  void _onFocusChanged() {
    // 当焦点失去时，调用onBlur回调
    if (!_focusNode.hasFocus && widget.onBlur != null) {
      widget.onBlur!();
    }
  }

  // 控制器文本变化时的回调
  void _onControllerChanged() {
    final String currentText = _controller.text;
    // 只有当文本值真正改变时才调用onChanged回调
    if (currentText != _lastText && widget.onChanged != null) {
      widget.onChanged!(currentText);
    }
    // 更新_lastText为当前文本
    _lastText = currentText;
  }

  // 处理清除按钮点击事件
  void _handleClear() {
    _controller.clear();

    // 通过FormFieldState触发验证
    final formFieldState = _formFieldKey.currentState;
    if (formFieldState != null) {
      // 更新值并触发验证
      formFieldState.didChange('');

      // 如果自动验证模式是always或onUserInteraction，则立即验证
      if (widget.autovalidateMode == AutovalidateMode.always ||
          widget.autovalidateMode == AutovalidateMode.onUserInteraction) {
        formFieldState.validate();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 根据条件构建后缀图标
    Widget? suffixWidget;
    final bool hasText = _controller.text.isNotEmpty;

    if (widget.suffixIcon != null) {
      // 用户提供了自定义后缀图标，优先使用
      suffixWidget = widget.suffixIcon;
    } else if (widget.showClearButton && widget.maxLines == 1) {
      // 显示清除按钮或占位（仅在单行模式下显示）
      suffixWidget = Opacity(
        opacity: hasText && _isHovering && !widget.disabled ? 1.0 : 0.0,
        child: AppButton(
          type: ButtonType.transparent,
          size: ButtonSize.small,
          iconData: Icons.clear,
          color: context.icon200,
          onPressed: hasText && !widget.disabled ? _handleClear : null,
        ),
      );
    }

    if (suffixWidget != null) {
      suffixWidget = Padding(padding: const EdgeInsets.all(5), child: suffixWidget);
    }
    // 构建输入框组件
    Widget inputField = FormField<String>(
      key: _formFieldKey,
      initialValue: _controller.text,
      validator: widget.validator,
      autovalidateMode: widget.autovalidateMode,
      builder: (FormFieldState<String> state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: _inputHeight,
              child: TextFormField(
                controller: _controller,
                keyboardType: widget.keyboardType,
                obscureText: widget.obscureText,
                focusNode: _focusNode,
                maxLength: widget.maxLength,
                maxLines: widget.maxLines,
                enabled: !widget.disabled,
                style: TextStyle(fontSize: _fontSize),
                autofocus: widget.autofocus,
                inputFormatters: widget.inputFormatters,

                /// 计数器
                buildCounter: !widget.showCounter
                    ? (
                        BuildContext context, {
                        required int currentLength,
                        required bool isFocused,
                        required int? maxLength,
                      }) => null
                    : null,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  prefixIcon: widget.prefixIcon,
                  suffixIcon: suffixWidget,
                  // 完全禁用Flutter默认的错误提示，确保不影响输入框高度
                  errorStyle: const TextStyle(height: 0, fontSize: 0),
                  errorText: null, // 始终为null，完全移除默认错误提示的影响
                  contentPadding: _contentPadding,
                  isDense: widget.maxLines > 1, // 只在多行文本时使用isDense
                  // 根据禁用状态调整样式
                  filled: true, // 始终填充，但根据状态使用不同的填充色
                  fillColor: widget.disabled
                      ? Theme.of(context).brightness == Brightness.light
                            ? AppInputTheme.disabledFillColorLight
                            : AppInputTheme.disabledFillColorDark
                      : null, // null时会使用主题默认的fillColor
                ),
                onChanged: (value) {
                  // 更新FormField状态并触发验证
                  state.didChange(value);
                },
              ),
            ),

            // 固定高度的错误信息区域，避免布局跳动
            if (widget.showErrMsg! && widget.required)
              SizedBox(
                height: _errorAreaHeight,
                child: Align(
                  alignment: Alignment.topLeft,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 200),
                    opacity: state.hasError ? 1.0 : 0.0,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        state.hasError ? (state.errorText ?? '') : '',
                        style: TextStyle(
                          fontSize: _errorFontSize,
                          color: AppColors.error,
                          height: 1.2,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );

    // 构建标签文本
    Widget? labelWidget;
    if (widget.label != null && widget.label!.isNotEmpty) {
      labelWidget = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.label!,
            style: TextStyle(
              fontSize: _fontSize,
              color: !widget.disabled
                  ? Theme.of(context).textTheme.bodyMedium?.color
                  : Theme.of(context).disabledColor,
            ),
          ),
          if (widget.required) Text(' *', style: TextStyle(fontSize: 20, color: AppColors.error)),
          const SizedBox(width: 10),
        ],
      );
    }

    // 根据标签位置构建最终的Widget
    Widget resultWidget;
    if (labelWidget == null) {
      // 没有标签，直接使用输入框
      resultWidget = MouseRegion(
        onEnter: (_) => setState(() => _isHovering = true),
        onExit: (_) => setState(() => _isHovering = false),
        child: inputField,
      );
    } else if (widget.labelPosition == LabelPosition.top) {
      // 标签在顶部
      resultWidget = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(padding: const EdgeInsets.only(bottom: 8.0), child: labelWidget),
          MouseRegion(
            onEnter: (_) => setState(() => _isHovering = true),
            onExit: (_) => setState(() => _isHovering = false),
            child: inputField,
          ),
        ],
      );
    } else {
      // 标签在左侧 - 修复标签与输入框的垂直对齐问题
      resultWidget = Row(
        crossAxisAlignment: CrossAxisAlignment.start, // 改为start对齐
        children: [
          // 标签容器，确保与输入框顶部对齐
          Container(
            height: _inputHeight, // 使用与输入框相同的高度
            alignment: Alignment.centerLeft, // 标签在容器内垂直居中
            child: widget.labelWidth != null
                ? SizedBox(width: widget.labelWidth, child: labelWidget)
                : labelWidget,
          ),
          Expanded(
            child: MouseRegion(
              onEnter: (_) => setState(() => _isHovering = true),
              onExit: (_) => setState(() => _isHovering = false),
              child: inputField,
            ),
          ),
        ],
      );
    }

    // 根据width参数决定是否限制宽度
    if (widget.width != null) {
      return SizedBox(width: widget.width, child: resultWidget);
    }

    return resultWidget;
  }
}
